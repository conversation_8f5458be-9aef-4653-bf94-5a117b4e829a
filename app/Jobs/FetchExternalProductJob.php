<?php

namespace App\Jobs;

use App\Events\ExternalDatabaseBusy;
use App\Events\ProductAlreadyExist;
use App\Events\ProductCreated;
use App\Events\ProductNotFound;
use App\Http\Client\OpenFoodFactsClient;
use App\Models\User;
use App\Repositories\ProductRepositoryInterface;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;

class FetchExternalProductJob implements ShouldQueue
{
    use Queueable;

    public $tries = 5;
    public $backoff = 30;

    /**
     * Create a new job instance.
     */
    public function __construct(public string $barcode, public User $user)
    {
    }

    /**
     * Execute the job.
     */
    public function handle(OpenFoodFactsClient $client, ProductRepositoryInterface $productRepository): void
    {
        try {
            $productData = $client->searchProduct($this->barcode);

            if ($productData) {
                $product = $productRepository->create($productData);
                broadcast(new ProductCreated($product, $this->user->id));
                $this->delete();
            } else {
                // Si no se encuentra el producto, emitir evento específico
                broadcast(new ProductNotFound($this->barcode, $this->user->id));
                $this->delete();
            }
        } catch (\Throwable $th) {
            // En caso de error, emitir evento de base de datos ocupada
            // dd($th->getMessage());
            $code = $th->getCode();
            if ('23505' == $th->getCode()) {
                broadcast(new ProductAlreadyExist($this->barcode, $this->user->id));

                $this->delete();

                return;
            }

            if ($this->attempts() === $this->tries) {
                broadcast(new ProductNotFound($this->barcode, $this->user->id));

                return;
            }

            if (429 == $code) {
                broadcast(new ExternalDatabaseBusy($this->user->id));

                return;
            }
            // Log del error para debugging
            Log::error('Error fetching external product: '.$th->getMessage(), [
                'barcode' => $this->barcode,
                'user_id' => $this->user->id,
                'exception' => $th,
            ]);
            throw $th;
        }
    }
}
