<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ProductNotFound implements ShouldBroadcast
{
    use Dispatchable;
    use InteractsWithSockets;
    use SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(public string $barcode, public int $userId)
    {
    }

    public function broadcastAs()
    {
        return 'product-not-found';
    }

    public function broadcastWith()
    {
        return [
            'barcode' => $this->barcode,
            'message' => 'El producto no encontrado en otras fuentes de datos, puedes ingresarlo manualmente',
        ];
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('product-search-external-'.$this->userId),
        ];
    }
}
