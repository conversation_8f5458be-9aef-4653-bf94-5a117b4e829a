<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ProductAlreadyExist implements ShouldBroadcast
{
    use Dispatchable;
    use InteractsWithSockets;
    use SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(public string $barcode, public int $userId)
    {
    }

    public function broadcastAs()
    {
        return 'product-already-exist';
    }

    public function broadcastWith()
    {
        return [
            'barcode' => $this->barcode,
            'message' => 'El producto ya existe, porfavor verifica el codigo de barras e intentalo nuevamente',
        ];
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('product-search-external-'.$this->userId),
        ];
    }
}
