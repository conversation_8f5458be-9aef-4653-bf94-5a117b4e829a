<?php

namespace App\Http\Client;

use Illuminate\Support\Facades\Http;

class OpenFoodFactsClient
{
    public function searchProduct(string $barcode): ?array
    {
        $response = Http::get("https://cl.openfoodfacts.net/api/v2/product/$barcode");

        if ($response->successful()) {
            $productData = $response->json();

            if (isset($productData['product'])) {
                return $this->mapProductData($productData['product']);
            }
        }

        if (429 == $response->status()) {
            throw new \Exception('Base de datos externa ocupada, lo intentaremos en 1 minuto.', 429);
        }
        // $response->throw();
        // broadcast(new ())
        return null;
    }

    private function mapProductData(array $productData)
    {
        $name = '';
        $brands = '';
        if (isset($productData['brands'])) {
            $brands = $productData['brands'];
        }
        if (isset($productData['product_name_es']) || isset($productData['product_name'])) {
            $name = ($productData['product_name_es'] ?? $productData['product_name']).' '.$brands;
        }

        $description = '';
        if (isset($productData['generic_name']) || isset($productData['generic_name_es'])) {
            $description = ($productData['generic_name_es'] ?? $productData['generic_name']).' '.$brands;
        }

        $image = $productData['image_url'] ?? 'https://img.freepik.com/premium-vector/new-product-banner-template-design_579179-1372.jpg?semt=ais_hybrid&w=740';
        // $categoryId = $productData['categories'] ?? 1;
        $categoryId = 1;

        return [
            'name' => $name,
            'description' => $description,
            'barcode' => $productData['code'],
            'image' => $image,
            'category_id' => $categoryId,
            'expiration_date' => null,
        ];
    }
}
