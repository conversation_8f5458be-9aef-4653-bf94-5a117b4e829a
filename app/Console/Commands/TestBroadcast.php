<?php

namespace App\Console\Commands;

use App\Events\ExternalDatabaseBusy;
use App\Events\ProductCreated;
use App\Events\ProductNotFound;
use App\Models\Product;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Auth;

class TestBroadcast extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:broadcast {event}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test broadcasting events';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $event = $this->argument('event');
        Auth::login(User::first());
        $user = Auth::user();

        switch ($event) {
            case 'product-created':
                $product = Product::first();
                if ($product) {
                    broadcast(new ProductCreated($product, $user->id));
                    $this->info('ProductCreated event broadcasted with existing product');
                } else {
                    // Crear un producto temporal para la prueba
                    $product = new Product([
                        'name' => 'Producto de Prueba',
                        'barcode' => '1234567890',
                        'description' => 'Producto creado para prueba de broadcast',
                        'category_id' => 1,
                    ]);
                    $product->id = 999; // ID temporal
                    broadcast(new ProductCreated($product, $user->id));
                    $this->info('ProductCreated event broadcasted with test product');
                }
                break;

            case 'product-not-found':
                broadcast(new ProductNotFound('1234567890', $user->id));
                $this->info('ProductNotFound event broadcasted');
                break;

            case 'database-busy':
                broadcast(new ExternalDatabaseBusy($user->id));
                $this->info('ExternalDatabaseBusy event broadcasted');
                break;

            default:
                $this->error('Invalid event. Use: product-created, product-not-found, or database-busy');
                break;
        }
    }
}
