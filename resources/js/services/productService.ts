import axios from 'axios';
import type { Product, Category, StockEntryRequest, StockEntryResponse } from '@/types/product';

export class ProductService {
    private static readonly BASE_URL = '/api/products';

    static async searchByBarcode(barcode: string): Promise<Product | null> {
        try {
            const response = await fetch(`${this.BASE_URL}/barcode/${barcode}`);
            
            if (response.status === 200) {
                return await response.json();
            }
            
            if (response.status === 404) {
                const data = await response.json();
                throw new Error(data.message || 'Producto no encontrado');
            }
            
            throw new Error('Error inesperado en la búsqueda');
        } catch (error) {
            console.error('Error searching product by barcode:', error);
            throw error;
        }
    }

    static async getCategories(): Promise<Category[]> {
        try {
            const response = await fetch('/categories');
            const data = await response.json();
            return data.data || [];
        } catch (error) {
            console.error('Error fetching categories:', error);
            throw new Error('Error al cargar las categorías');
        }
    }

    static async createStockEntry(stockEntry: StockEntryRequest): Promise<StockEntryResponse> {
        try {
            const response = await axios.post(route('stock.entry'), stockEntry);
            return response.data;
        } catch (error: any) {
            console.error('Error creating stock entry:', error);
            
            if (error.response?.data) {
                throw new Error(error.response.data.message || 'Error al crear el movimiento de stock');
            }
            
            throw new Error('Error de conexión al crear el movimiento de stock');
        }
    }

    static async checkProductInLocal(productId: number, localId: number): Promise<boolean> {
        try {
            const response = await fetch(`${this.BASE_URL}/${productId}/local/${localId}`);
            return response.status === 200;
        } catch (error) {
            console.error('Error checking product in local:', error);
            return false;
        }
    }
}

// Export individual functions for easier testing and usage
export const {
    searchByBarcode,
    getCategories,
    createStockEntry,
    checkProductInLocal,
} = ProductService;
