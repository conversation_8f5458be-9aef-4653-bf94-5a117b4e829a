import { ref } from 'vue';
import { useRadixToast } from './useRadixToast';
import { STOCK_ENTRY_CONSTANTS } from '@/constants/stockEntry';

interface Product {
    id: number;
    name: string;
    description: string;
    barcode: string;
    category_id: number;
    expiration_date: string | null;
    category: {
        id: number;
        name: string;
    };
    batches?: Array<{
        id: number;
        barcode: string | null;
        expiration_date: string | null;
        quantity: number;
    }>;
    stock?: Array<{
        id: number;
        local_id: number;
        product_id: number;
        stock: number;
        price: number;
        min_stock: number;
        max_stock: number;
        expiration_date: string;
        status: boolean;
    }>;
}

export function useProductSearch() {
    const productFound = ref<Product | null>(null);
    const isLoading = ref(false);
    const loadingMessage = ref('');
    const { showSuccess, showError } = useRadixToast();

    const searchProduct = async (barcode: string): Promise<Product | null> => {
        if (!barcode.trim()) {
            showError(STOCK_ENTRY_CONSTANTS.VALIDATION_MESSAGES.BARCODE_REQUIRED);
            return null;
        }

        isLoading.value = true;
        loadingMessage.value = 'Buscando producto...';

        try {
            const response = await fetch(`/api/products/barcode/${barcode}`);
            const data = await response.json();

            if (response.status === 200) {
                productFound.value = data;
                showSuccess(`${STOCK_ENTRY_CONSTANTS.SUCCESS_MESSAGES.PRODUCT_FOUND}${data.name}`);
                return data;
            }

            if (response.status === 404) {
                showError(data.message);
                return null;
            }

            throw new Error('Error inesperado en la búsqueda');
        } catch (error) {
            console.error('Error buscando producto:', error);
            showError(STOCK_ENTRY_CONSTANTS.ERROR_MESSAGES.SEARCH_ERROR);
            return null;
        } 
    };

    const resetProduct = () => {
        productFound.value = null;
        loadingMessage.value = '';
    };

    const setLoadingMessage = (message: string) => {
        loadingMessage.value = message;
    };

    return {
        productFound,
        isLoading,
        loadingMessage,
        searchProduct,
        resetProduct,
        setLoadingMessage,
    };
}
