import { ref } from 'vue';
import { useForm } from '@inertiajs/vue3';
import axios from 'axios';
import { useRadixToast } from './useRadixToast';
import { STOCK_ENTRY_CONSTANTS } from '@/constants/stockEntry';

interface Product {
    id: number;
    name: string;
    barcode: string;
}

interface StockEntryData {
    product_id: number;
    local_id: number;
    quantity: number;
    movement_reason: string;
    batch_id?: number | null;
}

export function useStockEntry() {
    const isSubmitting = ref(false);
    const { showSuccess, showError } = useRadixToast();

    const form = useForm({
        product_id: '',
        barcode: '',
        local_id: '',
        quantity: '',
        batch_id: '',
        movement_reason: STOCK_ENTRY_CONSTANTS.DEFAULT_MOVEMENT_REASON,
    });

    const validateForm = (product: Product | null): string | null => {
        if (!product) {
            return STOCK_ENTRY_CONSTANTS.VALIDATION_MESSAGES.PRODUCT_REQUIRED;
        }

        if (!form.local_id) {
            return STOCK_ENTRY_CONSTANTS.VALIDATION_MESSAGES.LOCAL_REQUIRED;
        }

        if (!form.quantity || parseInt(form.quantity) <= 0) {
            return STOCK_ENTRY_CONSTANTS.VALIDATION_MESSAGES.QUANTITY_REQUIRED;
        }

        if (!form.movement_reason.trim()) {
            return STOCK_ENTRY_CONSTANTS.VALIDATION_MESSAGES.MOVEMENT_REASON_REQUIRED;
        }

        return null;
    };

    const submitStockEntry = async (product: Product): Promise<boolean> => {
        const validationError = validateForm(product);
        if (validationError) {
            showError(validationError);
            return false;
        }

        isSubmitting.value = true;

        try {
            const stockEntryData: StockEntryData = {
                product_id: product.id,
                local_id: parseInt(form.local_id),
                quantity: parseInt(form.quantity),
                movement_reason: form.movement_reason.trim(),
                batch_id: form.batch_id ? parseInt(form.batch_id) : null,
            };

            const response = await axios.post(route('stock.entry'), stockEntryData);
            const data = response.data;

            if (data.success) {
                showSuccess(data.message || STOCK_ENTRY_CONSTANTS.SUCCESS_MESSAGES.STOCK_ENTRY_SUCCESS);

                console.log('Stock entry successful:', {
                    stock_movement: data.data?.stock_movement,
                    new_stock: data.data?.new_stock,
                    local_product: data.data?.local_product,
                });

                return true;
            } else {
                throw new Error(data.message || STOCK_ENTRY_CONSTANTS.ERROR_MESSAGES.STOCK_ENTRY_ERROR);
            }
        } catch (error: any) {
            console.error('Stock entry error:', error);

            let errorMessage = STOCK_ENTRY_CONSTANTS.ERROR_MESSAGES.STOCK_ENTRY_ERROR;

            if (error.response?.data?.message) {
                errorMessage = error.response.data.message;
            } else if (error.response?.data?.errors) {
                const errors = error.response.data.errors;
                const firstError = Object.values(errors)[0];
                errorMessage = Array.isArray(firstError) ? firstError[0] : firstError;
            } else if (error.message) {
                errorMessage = error.message;
            }

            showError(errorMessage);
            return false;
        } finally {
            isSubmitting.value = false;
        }
    };

    const resetForm = () => {
        form.reset();
    };

    return {
        form,
        isSubmitting,
        submitStockEntry,
        resetForm,
    };
}
