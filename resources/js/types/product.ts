export interface Category {
    id: number;
    name: string;
}

export interface Local {
    id: number;
    name: string;
}

export interface Batch {
    id: number;
    barcode: string | null;
    expiration_date: string | null;
    quantity: number;
}

export interface Stock {
    id: number;
    local_id: number;
    product_id: number;
    stock: number;
    price: number;
    min_stock: number;
    max_stock: number;
    expiration_date: string;
    status: boolean;
}

export interface Product {
    id: number;
    name: string;
    description: string;
    barcode: string;
    category_id: number;
    expiration_date: string | null;
    category: Category;
    batches?: Batch[];
    stock?: Stock[];
}

export interface StockMovement {
    id: number;
    product_id: number;
    local_id: number;
    quantity: number;
    movement_reason: string;
    batch_id?: number | null;
    created_at: string;
    updated_at: string;
}

export interface StockEntryRequest {
    product_id: number;
    local_id: number;
    quantity: number;
    movement_reason: string;
    batch_id?: number | null;
}

export interface StockEntryResponse {
    success: boolean;
    message: string;
    data?: {
        stock_movement: StockMovement;
        new_stock: number;
        local_product: any;
    };
}
