<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { Calendar, ChevronDown, ChevronUp } from 'lucide-vue-next';
import { CalendarDate, type DateValue } from '@internationalized/date';
import {
    DatePickerArrow,
    DatePickerCalendar,
    DatePicker<PERSON>ell,
    DatePicker<PERSON>ellTrigger,
    DatePickerContent,
    DatePickerField,
    DatePickerGrid,
    DatePickerGridBody,
    DatePickerGridHead,
    DatePickerGridRow,
    DatePickerHeadCell,
    DatePickerHeader,
    DatePickerHeading,
    DatePickerInput,
    DatePickerNext,
    DatePickerPrev,
    DatePickerRoot,
    DatePickerTrigger,
} from 'radix-vue';

// State for year selector
const showYearSelector = ref(false);
const currentYear = ref(new Date().getFullYear());

// Placeholder date for controlling the calendar view
const placeholder = ref<DateValue>(new CalendarDate(new Date().getFullYear(), new Date().getMonth() + 1, 1));

// Generate year options (current year ± 10 years for better UX)
const yearOptions = computed(() => {
    const years = [];
    const currentYearValue = new Date().getFullYear();
    const startYear = currentYearValue - 10;
    const endYear = currentYearValue + 10;

    for (let year = startYear; year <= endYear; year++) {
        years.push(year);
    }
    return years;
});

// Toggle year selector
const toggleYearSelector = () => {
    showYearSelector.value = !showYearSelector.value;
};

// Select year and navigate to it
const selectYear = (year: number) => {
    currentYear.value = year;
    showYearSelector.value = false;

    // Update the placeholder to navigate to the selected year
    // Keep the current month, but change the year
    placeholder.value = new CalendarDate(year, placeholder.value.month, 1);

    console.log(`Navigating to year: ${year}`);
};

// Handle placeholder updates from the DatePicker
const handlePlaceholderUpdate = (newPlaceholder: DateValue) => {
    placeholder.value = newPlaceholder;
    currentYear.value = newPlaceholder.year;
};

// Close year selector when clicking outside
const handleClickOutside = (event: Event) => {
    const target = event.target as HTMLElement;
    if (!target.closest('.year-selector-container')) {
        showYearSelector.value = false;
    }
};

// Setup event listeners
onMounted(() => {
    document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside);
});

</script>

<template>
    <div :class="'flex flex-col gap-2'">

        <DatePickerRoot class="z-50" id="date-field" locale="es-CL" :placeholder="placeholder"
            @update:placeholder="handlePlaceholderUpdate">
            <DatePickerField v-slot="{ segments }"
                class="flex select-none bg-white items-center justify-between rounded-lg text-center text-green10 border border-transparent p-1 w-40 data-[invalid]:border-red-500">
                <div class="flex items-center">
                    <template v-for="item in segments" :key="item.part">
                        <DatePickerInput v-if="item.part === 'literal'" :part="item.part">
                            {{ item.value }}
                        </DatePickerInput>
                        <DatePickerInput v-else :part="item.part"
                            class="rounded-md p-0.5 focus:outline-none focus:shadow-[0_0_0_2px] focus:shadow-black data-[placeholder]:text-green9 ">
                            {{ item.value }}
                        </DatePickerInput>
                    </template>
                </div>

                <DatePickerTrigger class="focus:shadow-[0_0_0_2px] rounded-md text-xl p-1 focus:shadow-black">
                    <Calendar />
                </DatePickerTrigger>
            </DatePickerField>

            <DatePickerContent :class="'z-50'" :side-offset="4"
                class="rounded-xl bg-white shadow-[0_10px_38px_-10px_hsla(206,22%,7%,.35),0_10px_20px_-15px_hsla(206,22%,7%,.2)] focus:shadow-[0_10px_38px_-10px_hsla(206,22%,7%,.35),0_10px_20px_-15px_hsla(206,22%,7%,.2),0_0_0_2px_theme(colors.green)] will-change-[transform,opacity] data-[state=open]:data-[side=top]:animate-slideDownAndFade data-[state=open]:data-[side=right]:animate-slideLeftAndFade data-[state=open]:data-[side=bottom]:animate-slideUpAndFade data-[state=open]:data-[side=left]:animate-slideRightAndFade">
                <DatePickerArrow class="fill-white" />
                <DatePickerCalendar locale="es-CL" v-slot="{ weekDays, grid }" class="p-4">
                    <DatePickerHeader class="flex items-center justify-between">
                        <DatePickerPrev
                            class="inline-flex items-center cursor-pointer text-black justify-center rounded-[9px] bg-transparent w-8 h-8 hover:bg-black hover:text-white active:scale-98 active:transition-all focus:shadow-[0_0_0_2px] focus:shadow-black">
                            <Calendar class="w-6 h-6" />
                        </DatePickerPrev>

                        <!-- Custom heading with year selector -->
                        <div class="relative year-selector-container">
                            <button @click="toggleYearSelector"
                                class="flex items-center gap-1 text-black font-medium hover:bg-gray-100 px-2 py-1 rounded-md transition-colors">
                                <DatePickerHeading />
                                <ChevronDown v-if="!showYearSelector" class="w-4 h-4" />
                                <ChevronUp v-else class="w-4 h-4" />
                            </button>

                            <!-- Year selector dropdown -->
                            <div v-if="showYearSelector"
                                class="absolute top-full left-1/2 transform -translate-x-1/2 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-50 min-w-[120px]">
                                <div class="py-2">
                                    <!-- Quick navigation buttons -->
                                    <div class="flex justify-between px-2 mb-2">
                                        <button @click="selectYear(currentYear - 10)"
                                            class="text-xs px-2 py-1 text-gray-600 hover:bg-gray-100 rounded"
                                            title="10 años atrás">
                                            -10
                                        </button>
                                        <button @click="selectYear(new Date().getFullYear())"
                                            class="text-xs px-2 py-1 text-blue-600 hover:bg-blue-50 rounded font-medium"
                                            title="Año actual">
                                            Hoy
                                        </button>
                                        <button @click="selectYear(currentYear + 10)"
                                            class="text-xs px-2 py-1 text-gray-600 hover:bg-gray-100 rounded"
                                            title="10 años adelante">
                                            +10
                                        </button>
                                    </div>

                                    <!-- Year list -->
                                    <div class="max-h-32 overflow-y-auto">
                                        <button v-for="year in yearOptions" :key="year" @click="selectYear(year)"
                                            class="block w-full px-3 py-1 text-sm text-left hover:bg-gray-100 focus:bg-gray-100 focus:outline-none transition-colors"
                                            :class="{
                                                'bg-blue-50 text-blue-700 font-medium': year === new Date().getFullYear(),
                                                'bg-gray-100 font-medium': year === currentYear && year !== new Date().getFullYear()
                                            }">
                                            {{ year }}
                                            <span v-if="year === new Date().getFullYear()"
                                                class="text-xs text-blue-500 ml-1">(actual)</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <DatePickerNext
                            class="inline-flex items-center cursor-pointer text-black justify-center rounded-[9px] bg-transparent w-8 h-8 hover:bg-black hover:text-white active:scale-98 active:transition-all focus:shadow-[0_0_0_2px] focus:shadow-black">
                            <Calendar class="w-6 h-6" />
                        </DatePickerNext>
                    </DatePickerHeader>
                    <div class="flex flex-col space-y-4 pt-4 sm:flex-row sm:space-x-4 sm:space-y-0">
                        <DatePickerGrid v-for="month in grid" :key="month.value.toString()"
                            class="w-full border-collapse select-none space-y-1">
                            <DatePickerGridHead>
                                <DatePickerGridRow class="mb-1 flex w-full justify-between">
                                    <DatePickerHeadCell v-for="day in weekDays" :key="day"
                                        class="w-8 rounded-md text-xs text-green8">
                                        {{ day }}
                                    </DatePickerHeadCell>
                                </DatePickerGridRow>
                            </DatePickerGridHead>
                            <DatePickerGridBody>
                                <DatePickerGridRow v-for="(weekDates, index) in month.rows" :key="`weekDate-${index}`"
                                    class="flex w-full">
                                    <DatePickerCell v-for="weekDate in weekDates" :key="weekDate.toString()"
                                        :date="weekDate">
                                        <DatePickerCellTrigger :day="weekDate" :month="month.value"
                                            class="relative flex items-center justify-center whitespace-nowrap rounded-[9px] border border-transparent bg-transparent text-sm font-normal text-black w-8 h-8 outline-none focus:shadow-[0_0_0_2px] focus:shadow-black hover:border-black data-[selected]:bg-black data-[selected]:font-medium data-[outside-view]:text-black/30 data-[selected]:text-white data-[unavailable]:pointer-events-none data-[unavailable]:text-black/30 data-[unavailable]:line-through before:absolute before:top-[5px] before:hidden before:rounded-full before:w-1 before:h-1 before:bg-white data-[today]:before:block data-[today]:before:bg-green9 data-[selected]:before:bg-white" />
                                    </DatePickerCell>
                                </DatePickerGridRow>
                            </DatePickerGridBody>
                        </DatePickerGrid>
                    </div>
                </DatePickerCalendar>
            </DatePickerContent>
        </DatePickerRoot>
    </div>
</template>
