<script setup lang="ts">
import { ref } from 'vue';
import Datepicker from '@/components/Datepicker.vue';

// Example dates
const selectedDate = ref(null);
const birthDate = ref(null);
const eventDate = ref(null);

// Handle date changes
const handleDateChange = (date: any, label: string) => {
    console.log(`${label} changed to:`, date);
};
</script>

<template>
    <div class="max-w-4xl mx-auto p-6 space-y-8">
        <div class="text-center">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">Datepicker con Selector de Año</h1>
            <p class="text-gray-600">Componente de selección de fechas con navegación rápida por años</p>
        </div>

        <!-- Basic Usage -->
        <div class="space-y-4">
            <h2 class="text-xl font-semibold text-gray-800">Funcionalidades del Selector de Año</h2>
            
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <h3 class="font-medium text-blue-800 mb-2">🎯 Nuevas Características:</h3>
                <ul class="text-sm text-blue-700 space-y-1">
                    <li>• <strong>Selector de año clickeable:</strong> Haz clic en el mes/año del header</li>
                    <li>• <strong>Navegación rápida:</strong> Botones -10, Hoy, +10 para saltar años</li>
                    <li>• <strong>Año actual destacado:</strong> El año actual se muestra en azul</li>
                    <li>• <strong>Cierre automático:</strong> Se cierra al hacer clic fuera del selector</li>
                    <li>• <strong>Rango optimizado:</strong> Muestra ±10 años del año actual</li>
                </ul>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="space-y-2">
                    <label class="text-sm font-medium text-gray-700">Fecha de Evento</label>
                    <Datepicker v-model="selectedDate" />
                    <p class="text-xs text-gray-500">
                        Valor: {{ selectedDate ? new Date(selectedDate).toLocaleDateString('es-ES') : 'No seleccionada' }}
                    </p>
                </div>

                <div class="space-y-2">
                    <label class="text-sm font-medium text-gray-700">Fecha de Nacimiento</label>
                    <Datepicker v-model="birthDate" />
                    <p class="text-xs text-gray-500">
                        Valor: {{ birthDate ? new Date(birthDate).toLocaleDateString('es-ES') : 'No seleccionada' }}
                    </p>
                </div>

                <div class="space-y-2">
                    <label class="text-sm font-medium text-gray-700">Fecha Importante</label>
                    <Datepicker v-model="eventDate" />
                    <p class="text-xs text-gray-500">
                        Valor: {{ eventDate ? new Date(eventDate).toLocaleDateString('es-ES') : 'No seleccionada' }}
                    </p>
                </div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="space-y-4">
            <h2 class="text-xl font-semibold text-gray-800">Cómo Usar el Selector de Año</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="bg-gray-50 rounded-lg p-4">
                    <h3 class="font-medium text-gray-800 mb-3">📅 Pasos para Seleccionar Año:</h3>
                    <ol class="text-sm text-gray-600 space-y-2">
                        <li><strong>1.</strong> Haz clic en el icono del calendario para abrir el datepicker</li>
                        <li><strong>2.</strong> Haz clic en el texto del mes/año en el header (ej: "Enero 2024")</li>
                        <li><strong>3.</strong> Se abrirá el selector de año con opciones rápidas</li>
                        <li><strong>4.</strong> Usa los botones -10, Hoy, +10 para navegación rápida</li>
                        <li><strong>5.</strong> O selecciona un año específico de la lista</li>
                        <li><strong>6.</strong> El calendario se actualizará al año seleccionado</li>
                    </ol>
                </div>

                <div class="bg-green-50 rounded-lg p-4">
                    <h3 class="font-medium text-green-800 mb-3">💡 Consejos de Uso:</h3>
                    <ul class="text-sm text-green-700 space-y-2">
                        <li>• El año actual aparece marcado como "(actual)" en azul</li>
                        <li>• Puedes hacer clic fuera del selector para cerrarlo</li>
                        <li>• Los botones de navegación rápida saltan de 10 en 10 años</li>
                        <li>• El botón "Hoy" te lleva directamente al año actual</li>
                        <li>• La lista muestra 21 años (±10 del año actual)</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Demo Section -->
        <div class="space-y-4">
            <h2 class="text-xl font-semibold text-gray-800">Demostración Interactiva</h2>
            
            <div class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                <div class="max-w-md mx-auto space-y-4">
                    <h3 class="text-lg font-medium text-gray-800">Prueba el Selector de Año</h3>
                    <p class="text-gray-600 text-sm">
                        Abre el datepicker y haz clic en el header para ver el selector de año en acción
                    </p>
                    
                    <div class="space-y-2">
                        <label class="text-sm font-medium text-gray-700">Fecha de Prueba</label>
                        <div class="max-w-xs mx-auto">
                            <Datepicker v-model="selectedDate" />
                        </div>
                    </div>
                    
                    <div v-if="selectedDate" class="mt-4 p-3 bg-blue-50 rounded-md">
                        <p class="text-sm text-blue-800">
                            <strong>Fecha seleccionada:</strong><br>
                            {{ new Date(selectedDate).toLocaleDateString('es-ES', { 
                                weekday: 'long', 
                                year: 'numeric', 
                                month: 'long', 
                                day: 'numeric' 
                            }) }}
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Technical Details -->
        <div class="space-y-4">
            <h2 class="text-xl font-semibold text-gray-800">Detalles Técnicos</h2>
            
            <div class="bg-gray-50 rounded-lg p-4">
                <h3 class="font-medium text-gray-800 mb-3">🔧 Implementación:</h3>
                <div class="text-sm text-gray-600 space-y-2">
                    <p><strong>Componente base:</strong> Radix Vue DatePicker</p>
                    <p><strong>Funcionalidad añadida:</strong> Selector de año personalizado</p>
                    <p><strong>Navegación:</strong> Botones rápidos y lista scrolleable</p>
                    <p><strong>UX:</strong> Cierre automático y indicadores visuales</p>
                    <p><strong>Rango:</strong> ±10 años del año actual (configurable)</p>
                </div>
            </div>
        </div>

        <!-- Code Example -->
        <div class="space-y-4">
            <h2 class="text-xl font-semibold text-gray-800">Ejemplo de Código</h2>
            
            <div class="bg-gray-900 rounded-lg p-4 overflow-x-auto">
                <pre class="text-green-400 text-sm"><code>&lt;script setup&gt;
import { ref } from 'vue';
import Datepicker from '@/components/Datepicker.vue';

const selectedDate = ref(null);
&lt;/script&gt;

&lt;template&gt;
    &lt;Datepicker v-model="selectedDate" /&gt;
&lt;/template&gt;</code></pre>
            </div>
        </div>
    </div>
</template>

<style scoped>
/* Estilos adicionales si son necesarios */
</style>
