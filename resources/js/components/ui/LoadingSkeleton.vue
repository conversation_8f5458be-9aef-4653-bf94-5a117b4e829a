<script setup lang="ts">
interface Props {
    message?: string;
    showMessage?: boolean;
}

withDefaults(defineProps<Props>(), {
    message: '',
    showMessage: true,
});
</script>

<template>
    <div class="mb-6 p-4 border rounded-lg bg-muted/30">
        <!-- Loading Message -->
        <div v-if="showMessage && message" class="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div class="flex items-center gap-2">
                <div class="h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                <p class="text-sm text-blue-700 font-medium">{{ message }}</p>
            </div>
        </div>
        
        <!-- Skeleton Animation -->
        <div class="animate-pulse">
            <div class="h-5 bg-muted rounded w-48 mb-4"></div>
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <div class="h-4 bg-muted rounded w-16 mb-2"></div>
                    <div class="h-5 bg-muted rounded w-32"></div>
                </div>
                <div>
                    <div class="h-4 bg-muted rounded w-16 mb-2"></div>
                    <div class="h-5 bg-muted rounded w-28"></div>
                </div>
                <div>
                    <div class="h-4 bg-muted rounded w-20 mb-2"></div>
                    <div class="h-5 bg-muted rounded w-24"></div>
                </div>
                <div>
                    <div class="h-4 bg-muted rounded w-32 mb-2"></div>
                    <div class="h-5 bg-muted rounded w-20"></div>
                </div>
            </div>
            <div class="mt-4">
                <div class="h-4 bg-muted rounded w-24 mb-2"></div>
                <div class="h-4 bg-muted rounded w-full mb-1"></div>
                <div class="h-4 bg-muted rounded w-3/4"></div>
            </div>
        </div>
    </div>
</template>
