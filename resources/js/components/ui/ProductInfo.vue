<script setup lang="ts">
interface Product {
    id: number;
    name: string;
    description: string;
    barcode: string;
    category?: {
        id: number;
        name: string;
    };
    expiration_date?: string | null;
}

interface Props {
    product: Product;
}

defineProps<Props>();
</script>

<template>
    <div class="mb-6 p-4 border rounded-lg bg-muted/30">
        <h4 class="font-medium mb-2">Información del Producto</h4>
        <div class="grid grid-cols-2 gap-4">
            <div>
                <p class="text-sm text-muted-foreground">Nombre:</p>
                <p class="font-medium">{{ product.name }}</p>
            </div>
            <div>
                <p class="text-sm text-muted-foreground">Código:</p>
                <p class="font-medium">{{ product.barcode }}</p>
            </div>
            <div>
                <p class="text-sm text-muted-foreground">Categoría:</p>
                <p class="font-medium">{{ product.category?.name || 'Sin categoría' }}</p>
            </div>
            <div v-if="product.expiration_date">
                <p class="text-sm text-muted-foreground">Fecha de Vencimiento:</p>
                <p class="font-medium">{{ product.expiration_date }}</p>
            </div>
        </div>
        <div class="mt-2">
            <p class="text-sm text-muted-foreground">Descripción:</p>
            <p class="text-sm">{{ product.description }}</p>
        </div>
    </div>
</template>
