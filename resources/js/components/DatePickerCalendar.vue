<script setup lang="ts">
import { <PERSON><PERSON><PERSON><PERSON>, ArrowRight } from 'lucide-vue-next';
import { DatePicker<PERSON><PERSON><PERSON><PERSON>, Date<PERSON><PERSON><PERSON><PERSON>, DatePicker<PERSON><PERSON>Trigger, DatePickerGrid, DatePickerGridBody, DateP<PERSON>GridHead, DatePickerGridRow, Date<PERSON><PERSON>H<PERSON><PERSON>ell, DateP<PERSON>Header, DateP<PERSON>Heading, DatePickerNext, DatePickerPrev } from 'radix-vue';


</script>

<template>

    <DatePickerCalendar locale="es-CL" v-slot="{ weekDays, grid }" class="p-4">
        <DatePickerHeader class="flex items-center justify-between">
            <DatePickerPrev
                class="inline-flex items-center cursor-pointer text-black justify-center rounded-[9px] bg-transparent w-8 h-8 hover:bg-black hover:text-white active:scale-98 active:transition-all focus:shadow-[0_0_0_2px] focus:shadow-black">
                <ArrowLeft class="w-6 h-6" />
            </DatePickerPrev>

            <DatePickerHeading class="text-black font-medium" />
            <DatePickerNext
                class="inline-flex items-center cursor-pointer text-black justify-center rounded-[9px] bg-transparent w-8 h-8 hover:bg-black hover:text-white active:scale-98 active:transition-all focus:shadow-[0_0_0_2px] focus:shadow-black">
                <ArrowRight class="w-6 h-6" />
            </DatePickerNext>
        </DatePickerHeader>
        <div class="flex flex-col space-y-4 pt-4 sm:flex-row sm:space-x-4 sm:space-y-0">
            <DatePickerGrid v-for="month in grid" :key="month.value.toString()"
                class="w-full border-collapse select-none space-y-1">
                <DatePickerGridHead>
                    <DatePickerGridRow class="mb-1 flex w-full justify-between">
                        <DatePickerHeadCell v-for="day in weekDays" :key="day"
                            class="w-8 rounded-md text-xs text-green8">
                            {{ day }}
                        </DatePickerHeadCell>
                    </DatePickerGridRow>
                </DatePickerGridHead>
                <DatePickerGridBody>
                    <DatePickerGridRow v-for="(weekDates, index) in month.rows" :key="`weekDate-${index}`"
                        class="flex w-full">
                        <DatePickerCell v-for="weekDate in weekDates" :key="weekDate.toString()" :date="weekDate">
                            <DatePickerCellTrigger :day="weekDate" :month="month.value"
                                class="relative flex items-center justify-center whitespace-nowrap rounded-[9px] border border-transparent bg-transparent text-sm font-normal text-black w-8 h-8 outline-none focus:shadow-[0_0_0_2px] focus:shadow-black hover:border-black data-[selected]:bg-black data-[selected]:font-medium data-[outside-view]:text-black/30 data-[selected]:text-white data-[unavailable]:pointer-events-none data-[unavailable]:text-black/30 data-[unavailable]:line-through before:absolute before:top-[5px] before:hidden before:rounded-full before:w-1 before:h-1 before:bg-white data-[today]:before:block data-[today]:before:bg-green9 data-[selected]:before:bg-white" />
                        </DatePickerCell>
                    </DatePickerGridRow>
                </DatePickerGridBody>
            </DatePickerGrid>
        </div>
    </DatePickerCalendar>
</template>
