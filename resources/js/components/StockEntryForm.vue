<script setup lang="ts">
import { ScanLineIcon, XIcon, PlusIcon, SearchIcon } from 'lucide-vue-next';
import { onMounted, ref, watch } from 'vue';
import { usePage } from '@inertiajs/vue3';
import BarcodeReader from './BarcodeReader.vue';
import AddProductWithoutStockModal from './AddProductWithoutStockModal.vue';
import BatchManagementModal from './BatchManagementModal.vue';
import AddProductToLocalModal from './AddProductToLocalModal.vue';
import LoadingSkeleton from '@/components/ui/LoadingSkeleton.vue';
import ProductInfo from '@/components/ui/ProductInfo.vue';

// Composables
import { useProductSearch } from '@/composables/useProductSearch';
import { useStockEntry } from '@/composables/useStockEntry';
import { useWebSocketEvents } from '@/composables/useWebSocketEvents';
import { useRadixToast } from '@/composables/useRadixToast';

// Services
import { ProductService } from '@/services/productService';

// Types
import type { Category, Local, Batch, Product } from '@/types/product';

// Constants
import { STOCK_ENTRY_CONSTANTS } from '@/constants/stockEntry';

// Component state
const showScanner = ref(false);
const showAddProductModal = ref(false);
const showBatchModal = ref(false);
const showAddToLocalModal = ref(false);
const categories = ref<Category[]>([]);
const scannedCode = ref('');
const scannedCodes = ref<string[]>([]);
const searchBarcode = ref('');
const useBatches = ref(false);
const batches = ref<Batch[]>([]);
const selectedLocal = ref<Local | null>(null);

const page = usePage();

// Composables
const { productFound, isLoading, loadingMessage, searchProduct, resetProduct, setLoadingMessage } = useProductSearch();
const { form, isSubmitting, submitStockEntry, resetForm: resetStockForm } = useStockEntry();
const { showSuccess, showError } = useRadixToast();

// Setup WebSocket events
useWebSocketEvents({
    productFound,
    isLoading,
    setLoadingMessage
});

// WebSocket events are now handled by useWebSocketEvents composable


onMounted(async () => {
    try {
        categories.value = await ProductService.getCategories();
    } catch (error) {
        console.error('Error loading categories:', error);
        showError(STOCK_ENTRY_CONSTANTS.ERROR_MESSAGES.CATEGORIES_LOAD_ERROR);
    }
});

const emits = defineEmits(['close']);

const handleSearchProduct = async () => {
    const barcodeToSearch = searchBarcode.value || scannedCode.value;
    if (!barcodeToSearch) {
        showError('Ingrese un código de barras para buscar');
        return;
    }

    const product = await searchProduct(barcodeToSearch);
    if (product) {
        form.product_id = String(product.id);
        form.barcode = product.barcode;

        // Si el producto tiene batches, cargarlos
        if (product.batches && product.batches.length > 0) {
            batches.value = product.batches;
        } else {
            batches.value = [];
        }
    }
};

const checkProductInLocal = (localId: string) => {
    if (!productFound.value) return;

    // Find the selected local
    const locals = page.props.locals as Local[];
    const local = locals.find(l => l.id === parseInt(localId));
    if (!local) return;

    // Check if product has stock information for this local
    const hasStockInLocal = productFound.value.stock &&
        productFound.value.stock.some((stock: any) => stock.local_id === parseInt(localId));

    if (!hasStockInLocal) {
        selectedLocal.value = local;
        showAddToLocalModal.value = true;
    }
};

watch(scannedCode, (newValue) => {
    if (newValue) {
        searchBarcode.value = newValue;
        handleSearchProduct();
    }
});

// Watch for local selection changes
watch(() => form.local_id, (newLocalId) => {
    if (newLocalId && productFound.value) {
        checkProductInLocal(newLocalId);
    }
});

const handleSubmit = async () => {
    if (!productFound.value) {
        showError('Debe buscar y seleccionar un producto primero');
        return;
    }

    const success = await submitStockEntry(productFound.value);
    if (success) {
        resetForm();
    }
};

const resetForm = () => {
    resetStockForm();
    resetProduct();
    searchBarcode.value = '';
    scannedCode.value = '';
    useBatches.value = false;
    batches.value = [];
};

const closeAddProductModal = () => {
    showAddProductModal.value = false;
};

const onProductCreated = (product: Product) => {
    productFound.value = product;
    form.product_id = String(product.id);
    form.barcode = product.barcode;
    showAddProductModal.value = false;
    showSuccess(STOCK_ENTRY_CONSTANTS.SUCCESS_MESSAGES.PRODUCT_CREATED);
};

const closeBatchModal = () => {
    showBatchModal.value = false;
};

const onBatchCreated = (batch: Batch) => {
    batches.value.push(batch);
    form.batch_id = String(batch.id);
    showBatchModal.value = false;
    showSuccess(STOCK_ENTRY_CONSTANTS.SUCCESS_MESSAGES.BATCH_CREATED);
};

const closeAddToLocalModal = () => {
    showAddToLocalModal.value = false;
};

const onProductAddedToLocal = (updatedProduct: Product) => {
    productFound.value = updatedProduct;
    showAddToLocalModal.value = false;
    showSuccess(STOCK_ENTRY_CONSTANTS.SUCCESS_MESSAGES.PRODUCT_ADDED_TO_LOCAL);
};
</script>

<template>
    <div class="rounded-lg border bg-card shadow-sm">
        <div class="p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold">Ingreso de Stock</h3>
                <button @click="emits('close')" class="rounded-full p-2 hover:bg-muted/50">
                    <XIcon class="h-4 w-4" />
                </button>
            </div>

            <!-- Barcode Scanner and Search Section -->
            <div class="mb-6">
                <div class="flex items-center gap-2 mb-4">
                    <button @click="showScanner = !showScanner"
                        class="inline-flex items-center justify-center gap-2 rounded-md border border-input bg-background px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground">
                        <ScanLineIcon class="h-4 w-4" />
                        {{ showScanner ? 'Ocultar Scanner' : 'Escanear Código' }}
                    </button>
                    <div class="flex-1 flex gap-2">
                        <input v-model="searchBarcode" type="text" placeholder="Ingrese código de barras"
                            class="flex h-10 w-5/6 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" />
                        <button @click="handleSearchProduct" :disabled="isLoading"
                            class="inline-flex items-center justify-center gap-2 rounded-md border border-input bg-background px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground disabled:opacity-50 disabled:cursor-not-allowed">
                            <SearchIcon v-if="!isLoading" class="h-4 w-4" />
                            <div v-if="isLoading"
                                class="h-4 w-4 border-2 border-current border-t-transparent rounded-full animate-spin">
                            </div>
                            {{ isLoading ? 'Buscando...' : 'Buscar Producto' }}
                        </button>
                    </div>
                    <button @click="showAddProductModal = true"
                        class="inline-flex items-center justify-center gap-2 rounded-md border border-input bg-background px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground">
                        <PlusIcon class="h-4 w-4" />
                        Nuevo Producto
                    </button>
                </div>

                <!-- Scanner View -->
                <div v-if="showScanner" class="border rounded-lg p-4">
                    <!-- Mobile-first layout -->
                    <div class="grid grid-rows-1 xl:grid-cols-2 gap-4">
                        <!-- Camera feed takes full width on mobile -->
                        <div class="w-full bg-black rounded-lg flex items-center justify-center">
                            <BarcodeReader @decode="(code: string) => {
                                scannedCode = code;
                                scannedCodes.push(code);
                            }" />
                        </div>

                        <!-- Scanned items below camera on mobile -->
                        <div class="space-y-2">
                            <div class="flex items-center justify-between">
                                <h4 class="font-medium">Códigos escaneados</h4>
                                <span class="text-sm text-muted-foreground">
                                    {{ scannedCodes.length }} en total
                                </span>
                            </div>
                            <div class="space-y-2 max-h-[200px] overflow-y-auto">
                                <div v-for="code in scannedCodes" :key="code"
                                    class="flex items-center justify-between p-2 bg-muted rounded-md">
                                    <span class="text-sm font-mono">{{ code }}</span>
                                    <button class="text-muted-foreground hover:text-destructive">
                                        <XIcon class="h-4 w-4" />
                                    </button>
                                </div>
                                <div v-if="scannedCodes.length === 0"
                                    class="text-sm text-muted-foreground text-center py-4">
                                    No hay códigos escaneados
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Loading Skeleton for Product Search -->
            <LoadingSkeleton v-if="isLoading && !productFound" :message="loadingMessage"
                :show-message="!!loadingMessage" />

            <!-- Product Info Section -->
            <ProductInfo v-if="productFound && !isLoading" :product="productFound" />

            <form @submit.prevent="handleSubmit" class="space-y-4">
                <div class="grid gap-4 items-center sm:grid-cols-2">
                    <!-- Formulario para ingreso de stock -->
                    <div class="space-y-2">
                        <label
                            class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                            Local
                        </label>
                        <select v-model="form.local_id" required
                            class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50">
                            <option value="">Selecciona un local</option>
                            <option v-for="local in page.props.locals as Local[]" :key="local.id" :value="local.id">
                                {{ local.name }}
                            </option>
                        </select>
                        <span class="text-sm text-red-600 dark:text-red-500">{{ form.errors.local_id }}</span>
                    </div>

                    <div class="space-y-2">
                        <label
                            class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                            Cantidad
                        </label>
                        <input v-model="form.quantity" type="number" step="1" min="1" required
                            class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" />
                        <span class="text-sm text-red-600 dark:text-red-500">{{ form.errors.quantity }}</span>
                    </div>

                    <!-- Batch Management Section -->
                    <div class="col-span-2 space-y-2">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-2">
                                <input v-model="useBatches" type="checkbox" id="useBatches"
                                    class="h-4 w-4 rounded border-input bg-background text-primary focus:outline-none focus:ring-1 focus:ring-ring" />
                                <label for="useBatches" class="text-sm font-medium">
                                    Gestionar por lotes
                                </label>
                            </div>
                            <button v-if="useBatches" type="button" @click="showBatchModal = true"
                                class="inline-flex items-center justify-center gap-2 rounded-md border border-input bg-background px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground">
                                <PlusIcon class="h-4 w-4" />
                                Nuevo Lote
                            </button>
                        </div>

                        <div v-if="useBatches && batches.length > 0" class="border rounded-lg p-4 mt-2">
                            <h4 class="font-medium mb-2">Lotes disponibles</h4>
                            <div class="space-y-2 max-h-[200px] overflow-y-auto">
                                <div v-for="batch in batches" :key="batch.id"
                                    class="flex items-center justify-between p-2 bg-muted rounded-md"
                                    :class="{ 'border-2 border-primary': form.batch_id === String(batch.id) }">
                                    <div>
                                        <p class="text-sm font-medium">Lote #{{ batch.id }}</p>
                                        <p v-if="batch.expiration_date" class="text-xs text-muted-foreground">
                                            Vence: {{ batch.expiration_date }}
                                        </p>
                                        <p v-if="batch.barcode" class="text-xs font-mono">{{ batch.barcode }}</p>
                                    </div>
                                    <button type="button" @click="form.batch_id = String(batch.id)"
                                        class="inline-flex items-center justify-center rounded-md border border-input bg-background px-2 py-1 text-xs hover:bg-accent hover:text-accent-foreground">
                                        Seleccionar
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div v-else-if="useBatches"
                            class="text-sm text-muted-foreground text-center py-4 border rounded-lg">
                            No hay lotes disponibles para este producto
                        </div>
                    </div>

                    <div class="col-span-2 space-y-2">
                        <label
                            class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                            Motivo del movimiento
                        </label>
                        <textarea v-model="form.movement_reason" rows="2" required
                            class="flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" />
                        <span class="text-sm text-red-600 dark:text-red-500">{{ form.errors.movement_reason }}</span>
                    </div>
                </div>

                <div class="flex justify-end space-x-2">
                    <button type="button" @click="resetForm"
                        class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2">
                        Limpiar
                    </button>
                    <button type="submit" :disabled="isSubmitting || !productFound"
                        class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2">
                        {{ isSubmitting ? 'Procesando...' : 'Registrar Ingreso' }}
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Modal para agregar producto sin stock -->
    <AddProductWithoutStockModal v-if="showAddProductModal" @close="closeAddProductModal"
        @product-created="onProductCreated" />

    <!-- Modal para gestionar lotes -->
    <BatchManagementModal v-if="showBatchModal" @close="closeBatchModal" @batch-created="onBatchCreated"
        :product-id="productFound?.id" />

    <!-- Modal para agregar producto al local -->
    <AddProductToLocalModal v-if="showAddToLocalModal && productFound && selectedLocal" @close="closeAddToLocalModal"
        @product-added-to-local="onProductAddedToLocal" :product="productFound" :local="selectedLocal" />
</template>
