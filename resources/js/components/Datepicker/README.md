# Datepicker Component con Selector de Año

Un componente de selección de fechas moderno y accesible construido con Radix Vue, que incluye un selector de año interactivo para navegación rápida.

## 🚀 Características

- ✅ **Selector de Año Interactivo**: Haz clic en el header para abrir el selector de año
- ✅ **Navegación Rápida**: Botones -10, Hoy, +10 para saltar años rápidamente
- ✅ **Año Actual Destacado**: El año actual se muestra resaltado en azul
- ✅ **Auto-cierre**: Se cierra automáticamente al hacer clic fuera del selector
- ✅ **Rango Optimizado**: Muestra ±10 años del año actual para mejor UX
- ✅ **Calendario Visual**: Interfaz de calendario intuitiva con navegación por meses
- ✅ **Formato Español**: Formato DD/MM/YYYY y locale es-CL
- ✅ **Accesibilidad**: Navegación por teclado y soporte ARIA completo
- ✅ **Personalizable**: Estilos CSS completamente personalizables
- ✅ **Reactivo**: Soporte completo para v-model y eventos

## 📦 Instalación

El componente utiliza las siguientes dependencias que ya están incluidas en el proyecto:

- `radix-vue` - Para los componentes base (DatePicker)
- `lucide-vue-next` - Para los iconos
- `tailwindcss` - Para los estilos

## 🎯 Uso Básico

```vue
<script setup lang="ts">
import { ref } from 'vue';
import Datepicker from '@/components/Datepicker.vue';

const selectedDate = ref(null);
</script>

<template>
    <Datepicker v-model="selectedDate" />
</template>
```

## 🎭 Funcionalidades del Selector de Año

### **Cómo Usar el Selector de Año**

1. **Abrir el datepicker**: Haz clic en el icono del calendario
2. **Acceder al selector**: Haz clic en el texto del mes/año en el header (ej: "Enero 2024")
3. **Navegación rápida**: Usa los botones:
   - **-10**: Retrocede 10 años
   - **Hoy**: Va al año actual
   - **+10**: Avanza 10 años
4. **Selección específica**: Haz clic en cualquier año de la lista
5. **Cierre**: Haz clic fuera del selector o selecciona un año

### **Indicadores Visuales**

- **Año actual**: Aparece en azul con la etiqueta "(actual)"
- **Año seleccionado**: Se resalta cuando es diferente al actual
- **Hover effects**: Transiciones suaves en todos los elementos interactivos

## 📋 Props

| Prop | Tipo | Default | Descripción |
|------|------|---------|-------------|
| `v-model` | `Date \| null` | `null` | Valor de la fecha seleccionada |

## 🎭 Eventos

| Evento | Tipo | Descripción |
|--------|------|-------------|
| `update:modelValue` | `(value: Date \| null) => void` | Emitido cuando cambia el valor (v-model) |

## 🎨 Ejemplos de Uso

### Uso Simple
```vue
<template>
    <Datepicker v-model="date" />
</template>
```

### Con Manejo de Eventos
```vue
<script setup>
import { ref } from 'vue';

const selectedDate = ref(null);

const handleDateChange = (date) => {
    console.log('Fecha seleccionada:', date);
};
</script>

<template>
    <Datepicker 
        v-model="selectedDate" 
        @update:modelValue="handleDateChange"
    />
</template>
```

### En Formularios
```vue
<template>
    <form @submit="handleSubmit">
        <div class="space-y-4">
            <div>
                <label class="block text-sm font-medium mb-1">
                    Fecha de Evento
                </label>
                <Datepicker v-model="form.eventDate" />
            </div>
            
            <div>
                <label class="block text-sm font-medium mb-1">
                    Fecha de Vencimiento
                </label>
                <Datepicker v-model="form.expirationDate" />
            </div>
        </div>
    </form>
</template>
```

## 🛠️ Configuración Interna

### **Rango de Años**
```typescript
// El componente muestra ±10 años del año actual
const yearOptions = computed(() => {
    const currentYear = new Date().getFullYear();
    const startYear = currentYear - 10;
    const endYear = currentYear + 10;
    // Genera array de años [2014, 2015, ..., 2034]
});
```

### **Navegación Rápida**
```typescript
// Botones de navegación rápida
const quickNavigation = {
    previous: () => selectYear(currentYear - 10),
    today: () => selectYear(new Date().getFullYear()),
    next: () => selectYear(currentYear + 10),
};
```

## ♿ Accesibilidad

### **Navegación por Teclado**
- `Tab`: Navegar entre elementos
- `Enter/Space`: Abrir calendario o seleccionar año
- `Escape`: Cerrar selector de año o calendario
- `Arrow Keys`: Navegar entre días en el calendario

### **ARIA y Screen Readers**
- Etiquetas descriptivas para todos los elementos interactivos
- Anuncios apropiados para cambios de estado
- Soporte completo para lectores de pantalla

## 🎨 Personalización

### **Estilos CSS**
El componente utiliza clases de Tailwind CSS que pueden ser personalizadas:

```css
/* Personalizar el selector de año */
.year-selector-container {
    /* Estilos del contenedor */
}

/* Personalizar el dropdown */
.year-selector-dropdown {
    /* Estilos del dropdown */
}
```

### **Temas**
```vue
<!-- Tema claro (por defecto) -->
<Datepicker v-model="date" />

<!-- Con clases personalizadas -->
<Datepicker 
    v-model="date" 
    class="custom-datepicker"
/>
```

## 🔧 Funcionalidades Técnicas

### **Auto-cierre**
```typescript
// Se cierra automáticamente al hacer clic fuera
const handleClickOutside = (event) => {
    if (!event.target.closest('.year-selector-container')) {
        showYearSelector.value = false;
    }
};
```

### **Gestión de Estado**
```typescript
// Estado del selector de año
const showYearSelector = ref(false);
const currentYear = ref(new Date().getFullYear());

// Toggle del selector
const toggleYearSelector = () => {
    showYearSelector.value = !showYearSelector.value;
};
```

## 🐛 Solución de Problemas

### **El selector de año no se abre**
- Asegúrate de que el datepicker esté abierto primero
- Verifica que estés haciendo clic en el texto del header, no en los botones de navegación

### **Los años no se muestran correctamente**
- El componente genera automáticamente ±10 años del año actual
- Si necesitas un rango diferente, puedes modificar la función `yearOptions`

### **El selector no se cierra**
- Haz clic fuera del área del selector
- El auto-cierre funciona detectando clics fuera del contenedor `.year-selector-container`

## 🔄 Roadmap

- [ ] Configuración personalizable del rango de años
- [ ] Selector de década para navegación más amplia
- [ ] Animaciones de transición mejoradas
- [ ] Soporte para múltiples locales
- [ ] Temas predefinidos
- [ ] Integración con bibliotecas de validación

## 📝 Contribuir

Para contribuir al componente:

1. Crea una rama para tu feature
2. Implementa los cambios
3. Agrega tests si es necesario
4. Actualiza la documentación
5. Crea un pull request

## 📄 Licencia

Este componente es parte del proyecto miAlmacen y sigue la misma licencia del proyecto principal.
