export const STOCK_ENTRY_CONSTANTS = {
    DEFAULT_MOVEMENT_REASON: 'Ingreso de stock',
    LOADING_MESSAGE_DURATION: 2000,
    TOAST_DURATION: 3000,
    VALIDATION_MESSAGES: {
        PRODUCT_REQUIRED: 'Debe buscar y seleccionar un producto primero',
        LOCAL_REQUIRED: 'Debe seleccionar un local',
        QUANTITY_REQUIRED: 'Debe ingresar una cantidad válida mayor a 0',
        MOVEMENT_REASON_REQUIRED: 'Debe ingresar un motivo para el movimiento',
        BARCODE_REQUIRED: 'Ingrese un código de barras para buscar',
    },
    SUCCESS_MESSAGES: {
        PRODUCT_FOUND: 'Producto encontrado: ',
        STOCK_ENTRY_SUCCESS: 'Stock ingresado correctamente',
        PRODUCT_CREATED: 'Producto creado correctamente',
        BATCH_CREATED: 'Lote creado correctamente',
        PRODUCT_ADDED_TO_LOCAL: 'Producto agregado al local correctamente',
    },
    ERROR_MESSAGES: {
        SEARCH_ERROR: 'Error al buscar el producto',
        STOCK_ENTRY_ERROR: 'Error al ingresar el stock',
        CATEGORIES_LOAD_ERROR: 'Error al cargar las categorías',
        CONNECTION_ERROR: 'Error de conexión',
    },
} as const;

export const WEBSOCKET_CHANNELS = {
    PRODUCT_CREATED_EXTERNAL: 'product-created-external',
    EXTERNAL_DATABASE_BUSY: 'external-database-busy',
    PRODUCT_SEARCH_EXTERNAL: 'product-search-external',
} as const;

export const WEBSOCKET_EVENTS = {
    PRODUCT_CREATED: '.product-created',
    EXTERNAL_DATABASE_BUSY: '.external-database-busy',
    PRODUCT_NOT_FOUND: '.product-not-found',
    PRODUCT_ALREADY_EXIST: '.product-already-exist',
} as const;
